using Shared.Domain;

namespace Requests.Domain;

public class Transition : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid FromNodeId { get; set; }
    public Guid ToNodeId { get; set; }
    public string Name { get; set; } = string.Empty;
    public Node FromNode { get; set; } = null!;
    public Node ToNode { get; set; } = null!;
    public string SourceHandler { get; set; }
    public string TargetHandler { get; set; }
    public ICollection<TransitionRule> Rules { get; set; } = [];
}
