using Requests.Domain.Enums;

namespace Requests.Application.Rules;

public record RuleExecutionDto(
    Guid Id,
    Guid TransitionRuleId,
    Guid TicketId,
    Guid ExecutedByUserId,
    DateTime ExecutedAt,
    RuleExecutionStatus Status,
    string InputData,
    string OutputData,
    string ErrorMessage,
    int ExecutionTimeMs,
    string TransitionRuleName,
    string TransitionRuleDescription
);

public record RuleExecutionListItemDto(
    Guid Id,
    Guid TransitionRuleId,
    Guid TicketId,
    Guid ExecutedByUserId,
    DateTime ExecutedAt,
    RuleExecutionStatus Status,
    string ErrorMessage,
    int ExecutionTimeMs,
    string TransitionRuleName
);
