using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Requests.Domain.Enums;
using Shared.Application;
using Shared.Endpoints;

namespace Requests.Application.Rules.ListRuleExecutions;

internal sealed class ListRuleExecutionsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/rule-executions", async (
            Guid? transitionRuleId,
            Guid? ticketId,
            Guid? executedByUserId,
            RuleExecutionStatus? status,
            DateTime? executedAtFrom,
            DateTime? executedAtTo,
            int pageNumber,
            int pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new ListRuleExecutionsQuery(
                transitionRuleId,
                ticketId,
                executedByUserId,
                status,
                executedAtFrom,
                executedAtTo,
                pageNumber == 0 ? 1 : pageNumber,
                pageSize == 0 ? 10 : pageSize
            );
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.RuleExecutions")
        .WithGroupName("apiv1")
        .Produces<PagedResult<RuleExecutionListItemDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.Rules");

        app.MapGet("/api/v1/requests/tickets/{ticketId}/rule-executions", async (
            Guid ticketId,
            Guid? transitionRuleId,
            Guid? executedByUserId,
            RuleExecutionStatus? status,
            DateTime? executedAtFrom,
            DateTime? executedAtTo,
            int pageNumber,
            int pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new ListRuleExecutionsQuery(
                transitionRuleId,
                ticketId,
                executedByUserId,
                status,
                executedAtFrom,
                executedAtTo,
                pageNumber == 0 ? 1 : pageNumber,
                pageSize == 0 ? 10 : pageSize
            );
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.RuleExecutions")
        .WithGroupName("apiv1")
        .Produces<PagedResult<RuleExecutionListItemDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.Rules");

        app.MapGet("/api/v1/requests/rules/{transitionRuleId}/executions", async (
            Guid transitionRuleId,
            Guid? ticketId,
            Guid? executedByUserId,
            RuleExecutionStatus? status,
            DateTime? executedAtFrom,
            DateTime? executedAtTo,
            int pageNumber,
            int pageSize,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new ListRuleExecutionsQuery(
                transitionRuleId,
                ticketId,
                executedByUserId,
                status,
                executedAtFrom,
                executedAtTo,
                pageNumber == 0 ? 1 : pageNumber,
                pageSize == 0 ? 10 : pageSize
            );
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.RuleExecutions")
        .WithGroupName("apiv1")
        .Produces<PagedResult<RuleExecutionListItemDto>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Requests.Rules");
    }
}
