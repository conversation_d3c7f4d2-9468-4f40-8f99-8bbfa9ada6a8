using MediatR;
using Requests.Domain.Enums;
using Shared.Application;

namespace Requests.Application.Rules.ListRuleExecutions;

public record ListRuleExecutionsQuery(
    Guid? TransitionRuleId = null,
    Guid? TicketId = null,
    Guid? ExecutedByUserId = null,
    RuleExecutionStatus? Status = null,
    DateTime? ExecutedAtFrom = null,
    DateTime? ExecutedAtTo = null,
    int PageNumber = 1,
    int PageSize = 10
) : IRequest<PagedResult<RuleExecutionListItemDto>>;
