using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Rules.ListRuleExecutions;

internal sealed class ListRuleExecutionsQueryHandler(IRequestsDbContext context)
    : IRequestHandler<ListRuleExecutionsQuery, PagedResult<RuleExecutionListItemDto>>
{
    public async Task<PagedResult<RuleExecutionListItemDto>> <PERSON>le(
        ListRuleExecutionsQuery request,
        CancellationToken cancellationToken)
    {
        var query = context.RuleExecutions
            .Include(re => re.TransitionRule)
            .AsQueryable();

        // Filters
        if (request.TransitionRuleId.HasValue)
        {
            query = query.Where(re => re.TransitionRuleId == request.TransitionRuleId.Value);
        }

        if (request.TicketId.HasValue)
        {
            query = query.Where(re => re.TicketId == request.TicketId.Value);
        }

        if (request.ExecutedByUserId.HasValue)
        {
            query = query.Where(re => re.ExecutedByUserId == request.ExecutedByUserId.Value);
        }

        if (request.Status.HasValue)
        {
            query = query.Where(re => re.Status == request.Status.Value);
        }

        if (request.ExecutedAtFrom.HasValue)
        {
            query = query.Where(re => re.ExecutedAt >= request.ExecutedAtFrom.Value);
        }

        if (request.ExecutedAtTo.HasValue)
        {
            query = query.Where(re => re.ExecutedAt <= request.ExecutedAtTo.Value);
        }

        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await context.RuleExecutions.CountAsync(cancellationToken);

        var ruleExecutions = await query
            .OrderByDescending(re => re.ExecutedAt)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(re => new RuleExecutionListItemDto(
                re.Id,
                re.TransitionRuleId,
                re.TicketId,
                re.ExecutedByUserId,
                re.ExecutedAt,
                re.Status,
                re.ErrorMessage,
                re.ExecutionTimeMs,
                re.TransitionRule.Name
            ))
            .ToListAsync(cancellationToken);

        var pagedResult = new PagedResult<RuleExecutionListItemDto>(ruleExecutions)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = filteredCount
        };

        return pagedResult;
    }
}
