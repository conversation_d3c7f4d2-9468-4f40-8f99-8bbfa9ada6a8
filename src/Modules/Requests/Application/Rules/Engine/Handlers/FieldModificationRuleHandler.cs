using Microsoft.Extensions.Logging;
using Requests.Application.Rules.Engine.Configurations;
using Requests.Domain;
using Requests.Domain.Enums;

namespace Requests.Application.Rules.Engine.Handlers;

public class FieldModificationRuleHandler(
    ILogger<FieldModificationRuleHandler> logger) : BaseRuleHandler
{
    public override RuleType RuleType => RuleType.FieldModification;

    public static readonly string FormConfigurationJson = """
        {
      "title": "<PERSON>rm<PERSON> Kuralı",
      "description": "Geçiş sırasında ticket alanlarını otomatik olarak değiştirir",
      "fields": [
        {
          "name": "FieldName",
          "label": "Alan Adı",
          "type": "Dropdown",
          "multiple": false,
          "required": true,
          "options": {
            "items": [
              {
                "value": "Title",
                "label": "Başlık"
              },
              {
                "value": "Description",
                "label": "Açıklama"
              },
              {
                "value": "Priority",
                "label": "Öncelik"
              },
              {
                "value": "Status",
                "label": "Durum"
              },
              {
                "value": "AssignedUserId",
                "label": "Atanan <PERSON>llan<PERSON>"
              },
              {
                "value": "AssignedDepartmentId",
                "label": "Atanan Departman"
              },
              {
                "value": "DueDate",
                "label": "Bitiş Tarihi"
              },
              {
                "value": "EstimatedHours",
                "label": "Tahmini Saat"
              },
              {
                "value": "ActualHours",
                "label": "Gerçek Saat"
              },
              {
                "value": "Tags",
                "label": "Etiketler"
              },
              {
                "value": "CustomField1",
                "label": "Özel Alan 1"
              },
              {
                "value": "CustomField2",
                "label": "Özel Alan 2"
              },
              {
                "value": "CustomField3",
                "label": "Özel Alan 3"
              }
            ]
          }
        },
        {
          "name": "Type",
          "label": "Değişiklik Tipi",
          "type": "Dropdown",
          "multiple": false,
          "required": true,
          "options": {
            "items": [
              {
                "value": 1,
                "label": "Sabit Değer Ata"
              },
              {
                "value": 2,
                "label": "Şu Anki Tarih/Saat Ata"
              },
              {
                "value": 3,
                "label": "Mevcut Kullanıcı Ata"
              },
              {
                "value": 4,
                "label": "Hesaplanmış Değer Ata"
              }
            ]
          }
        },
        {
          "name": "Value",
          "label": "Değer",
          "type": "TextBox",
          "showTopField": "Type",
          "showTopValue": [
            1
          ],
          "required": false
        },
        {
          "name": "DescriptionTag",
          "label": "Değişiklik Tipi",
          "type": "Tag",
          "multiple": false,
          "required": true,
          "showTopField": "Type",
          "showTopValue": [
            4
          ],
          "options": {
            "items": [
              {
                "value": "{{NOW}}",
                "label": "Çalışma Tarih/Saati"
              },
              {
                "value": "{{CURRENT_USER_ID}}",
                "label": "Mevcut Kullanıcı Ata"
              },
              {
                "value": "{{TICKET_ID}}",
                "label": "Mevcut Kullanıcı Ata"
              },
              {
                "value": "{{FROM_NODE_ID}}",
                "label": "Önceki Status"
              },
              {
                "value": "{{TO_NODE_ID}}",
                "label": "Sonraki Status"
              },
              {
                "value": "{{TICKET.Title}}",
                "label": "Talep Başlığı"
              },
              {
                "value": "{{TICKET.Description}}",
                "label": "Talep Açıklaması"
              },
              {
                "value": "{{TICKET.Priority}}",
                "label": "Talep Önceliği"
              },
              {
                "value": "{{TICKET.Status}}",
                "label": "Talep Durumu"
              },
              {
                "value": "{{TICKET.UserId}}",
                "label": "Talep Atanan Kullanıcı"
              },
              {
                "value": "{{USER.Code}}",
                "label": "Talep Atanan Kullanıcı Kodu"
              },
              {
                "value": "{{USER.FullName}}",
                "label": "Talep Atanan Kullanıcı Tam Adı"
              }
            ]
          }
        },
        {
          "name": "Value",
          "label": "Değer",
          "type": "TextArea",
          "placeholder":"Yukardaki tagları kullanarak hesaplanmış değeri belirleyebilirsiniz.",
          "showTopField": "Type",
          "showTopValue": [
            4
          ],
          "required": false
        }
      ]
    }
    """;

    public override async Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var modification = GetConfiguration<FieldModificationConfiguration>(rule);
            var result = RuleResult.Success();

            var newValue = await CalculateNewValueAsync(modification, context);
            result.ModifiedFields[modification.FieldName] = newValue;

            logger.LogInformation("Field {FieldName} modified to {NewValue} for ticket {TicketId}",
                modification.FieldName, newValue, context.TicketId);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Field modification rule execution failed for rule {RuleId}", rule.Id);
            return RuleResult.Failure("Alan değiştirme sırasında hata oluştu");
        }
    }

    private async Task<object> CalculateNewValueAsync(FieldModificationConfiguration modification, TransitionContext context)
    {
        await Task.CompletedTask;

        return modification.Type switch
        {
            ModificationType.SetValue => modification.Value,
            ModificationType.SetCurrentDateTime => DateTime.UtcNow,
            ModificationType.SetCurrentUser => context.UserId,
            ModificationType.SetCalculatedValue => CalculateValue(modification.Value, context),
            _ => modification.Value
        };
    }

    private object CalculateValue(string expression, TransitionContext context)
    {
        // Template değişkenlerini değiştir
        var result = expression
            .Replace("{{NOW}}", DateTime.UtcNow.ToString())
            .Replace("{{CURRENT_USER_ID}}", context.UserId.ToString())
            .Replace("{{TICKET_ID}}", context.TicketId.ToString())
            .Replace("{{FROM_NODE_ID}}", context.FromNode.Id.ToString())
            .Replace("{{TO_NODE_ID}}", context.ToNode.Id.ToString());

        // Ticket alanlarından değer al
        foreach (var kvp in context.TicketData)
        {
            result = result.Replace($"{{{{TICKET.{kvp.Key}}}}}", kvp.Value?.ToString() ?? string.Empty);
        }

        // User alanlarından değer al
        foreach (var kvp in context.UserData)
        {
            result = result.Replace($"{{{{USER.{kvp.Key}}}}}", kvp.Value?.ToString() ?? string.Empty);
        }

        return result;
    }
}
