using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Transitions.GetTransition;

public class GetTransitionQueryHandler(IRequestsDbContext context)
    : IRequestHandler<GetTransitionQuery, Result<TransitionDto>>
{
    public async Task<Result<TransitionDto>> <PERSON><PERSON>(GetTransitionQuery request, CancellationToken cancellationToken)
    {
        var transition = await context.Transitions
            .Include(t => t.FromNode)
            .Include(t => t.ToNode)
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (transition is null)
        {
            return Result.Failure<TransitionDto>("Transition bulunamadı");
        }

        var transitionDto = new TransitionDto
        {
            Id = transition.Id,
            FromNodeId = transition.FromNodeId,
            ToNodeId = transition.ToNodeId,
            Name = transition.Name,
            InsertDate = transition.InsertDate,
            UpdateDate = transition.UpdateDate,
            FromNodeName = transition.FromNode.Name,
            ToNodeName = transition.ToNode.Name,
            SourceHandler = transition.SourceHandler,
            TargetHandler = transition.TargetHandler
        };

        return Result.Success(transitionDto);
    }
}
