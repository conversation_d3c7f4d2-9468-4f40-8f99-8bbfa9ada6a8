using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Transitions.UpdateTransition;

public class UpdateTransitionCommandHandler(IRequestsDbContext context)
    : IRequestHandler<UpdateTransitionCommand, Result>
{
    public async Task<Result> Handle(UpdateTransitionCommand request, CancellationToken cancellationToken)
    {
        var transition = await context.Transitions
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (transition is null)
        {
            return Result.Failure("Transition bulunamadı");
        }

        transition.Name = request.Name;
        transition.FromNodeId = request.FromNodeId;
        transition.ToNodeId = request.ToNodeId;
        transition.SourceHandler = request.SourceHandler;
        transition.TargetHandler = request.TargetHandler;

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
