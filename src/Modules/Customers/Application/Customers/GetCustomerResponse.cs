using Customers.Domain;

namespace Customers.Application.Customers;

public record GetCustomerResponse(
    Guid Id,
    string Name,
    string Surname,
    string Email,
    string Phone,
    string? PhonePrefix,
    string TaxOffice,
    string TaxNumber,
    string IdentificationNumber,
    string Country,
    string MainLanguage,
    string AvailableLanguage,
    string Description,
    string MailBcc,
    CustomerType? Type,
    CustomerKind? Kind,
    CustomerStatus? Status,
    Guid? CustomerSourceId,
    string CustomerSourceName,
    Guid? NotificationWayId,
    string? NotificationWayName,
    List<ClassificationDto>? Classifications,
    Guid? SectorId,
    Guid? ProfessionId,
    List<Guid>? AdvisorIds,
    Dictionary<string, string>? AttributeData,
    IReadOnlyList<ContactDto> Contacts);
