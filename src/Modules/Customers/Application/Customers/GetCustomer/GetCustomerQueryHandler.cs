using Customers.Application.Abstractions;
using Customers.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Customers.GetCustomer;

internal sealed class GetCustomerQueryHandler(ICustomersDbContext context) : IRequestHandler<GetCustomerQuery, Result<GetCustomerResponse>>
{
    public async Task<Result<GetCustomerResponse>> Handle(GetCustomerQuery request, CancellationToken cancellationToken)
    {
        var customer = await context.Customers
            .Include(x => x.Contacts)
            .Include(x => x.CustomerClassifications)
                .ThenInclude(x => x.Classification)
            .Include(x => x.Sector)
            .Include(x => x.Profession)
            .Include(x => x.CustomerSource)
            .Include(x => x.NotificationWay)
            .FirstOrDefaultAsync(x => x.Id == request.Id && !x.IsDeleted, cancellationToken);

        if (customer is null)
        {
            return Result.Failure<GetCustomerResponse>(CustomerErrors.CustomerNotFound(request.Id));
        }
        var classifications = customer.CustomerClassifications != null
            ? customer.CustomerClassifications
                .Where(cc => cc.Classification != null)
                .Select(cc => new ClassificationDto(cc.ClassificationId, cc.Classification.Name))
                .ToList()
            : [];

        var result = new GetCustomerResponse(
            customer.Id,
            customer.Name,
            customer.Surname,
            customer.Email,
            customer.Phone,
            customer.PhonePrefix,
            customer.TaxOffice,
            customer.TaxNumber,
            customer.IdentificationNumber,
            customer.Country,
            customer.MainLanguage,
            customer.AvailableLanguage,
            customer.Description,
            customer.MailBcc,
            customer.Type,
            customer.Kind,
            customer.Status,
            customer.CustomerSourceId,
            customer.CustomerSource?.Name ?? string.Empty,
            customer.NotificationWayId,
            customer.NotificationWay?.Name,
            classifications,
            customer.SectorId,
            customer.ProfessionId,
            customer.AdvisorIds,
            customer.AttributeData,
            [.. customer.Contacts.Select(c => new ContactDto(
                c.Id,
                c.CustomerId,
                c.ContactInfo,
                c.IsDefault,
                c.Name,
                c.Surname,
                c.Email,
                c.Phone,
                c.PhonePrefix,
                c.Language,
                c.Type.ToString(),
                c.Status,
                c.IsNotification,
                c.IsSms,
                c.IsEmail
            ))]);
        return Result.Success(result);
    }
}
