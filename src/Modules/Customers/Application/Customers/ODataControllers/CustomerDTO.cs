using System.ComponentModel.DataAnnotations;
using Customers.Domain;

namespace Customers.Application.Customers.ODataControllers;

public class CustomerDTO
{
    public Guid Id { get; set; }

    [Required]
    [MaxLength(100)]
    public string Name { get; set; }

    [Required]
    [MaxLength(100)]
    public string Surname { get; set; }

    [Required]
    [MaxLength(255)]
    [EmailAddress]
    public string Email { get; set; }

    [Required]
    [MaxLength(20)]
    [Phone]
    public string Phone { get; set; }
    public string PhonePrefix { get; set; }

    [Required]
    public CustomerType Type { get; set; }

    public List<Guid>? AdvisorIds { get; set; } = [];

    public Dictionary<string, string>? AttributeData { get; set; }

    public ICollection<ODataContactDTO> Contacts { get; set; }
}

public class ODataContactDTO
{
    public Guid Id { get; set; }

    [Required]
    [MaxLength(255)]
    public string ContactInfo { get; set; }

    public bool IsDefault { get; set; }

    [Required]
    [MaxLength(100)]
    public string Name { get; set; }

    [Required]
    [MaxLength(100)]
    public string Surname { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public string PhonePrefix { get; set; }
    public string Language { get; set; }

    [Required]
    public ContactType Type { get; set; }

    public bool? IsNotification { get; set; }
    public bool? IsSms { get; set; }
    public bool? IsEmail { get; set; }
}
