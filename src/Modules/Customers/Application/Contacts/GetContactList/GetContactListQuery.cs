using MediatR;
using Shared.Application;

namespace Customers.Application.Contacts.GetContactList;

public record GetContactListQuery(
    Guid? CustomerId = null,
    int PageNumber = 1,
    int PageSize = 10,
    string? SearchTerm = null
) : IRequest<Result<GetContactListResponse>>;

public record GetContactListResponse(
    int TotalCount,
    int PageNumber,
    int PageSize,
    IReadOnlyList<ContactDto> Items);
