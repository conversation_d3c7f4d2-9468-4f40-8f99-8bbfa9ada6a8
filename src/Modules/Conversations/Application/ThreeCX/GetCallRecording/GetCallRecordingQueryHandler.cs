using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.StaticFiles;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Application.EventDbLogger;
using Shared.Domain;

namespace Conversations.Application.ThreeCX.GetCallRecording;

public class GetCallRecordingQueryHandler(
    AppSettings appSettings,
    IWorkContext workContext,
    IEventBus eventBus
) : IRequestHandler<GetCallRecordingQuery, IResult>
{
    private readonly AppSettings _appSettings = appSettings;
    private readonly IWorkContext _workContext = workContext;
    private readonly IEventBus _eventBus = eventBus;

    public async Task<IResult> Handle(GetCallRecordingQuery request, CancellationToken cancellationToken)
    {
        var path = Path.Combine(_appSettings.ThreeCXRecordingPath, request.CallRecordingPath);
        if (!File.Exists(path))
        {
            throw new ArgumentException("Dosya bulunamadı");
        }
        var fileStream = File.OpenRead(path);
        await _eventBus.PublishAsync(new ExportAuditLoggedEvent(
                _workContext.UserId,
                "Conversations",
                0,
                false,
                0,
                request.CallRecordingPath,
                null,
                "GetCallRecordingQueryHandler"
            ), cancellationToken);
        var filename = request.CallRecordingPath.Replace("/", "_");
        var fileProvider = new FileExtensionContentTypeProvider();
        if (!fileProvider.TryGetContentType(request.CallRecordingPath, out string contentType))
        {
            throw new ArgumentOutOfRangeException($"Unable to find Content Type for file name {request.CallRecordingPath}.");
        }
        return Results.Stream(fileStream, contentType, filename);
    }
}
