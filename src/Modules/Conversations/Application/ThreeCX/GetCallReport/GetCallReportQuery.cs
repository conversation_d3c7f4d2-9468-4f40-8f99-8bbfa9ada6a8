using MediatR;
using Shared.Application;

namespace Conversations.Application.ThreeCX.GetCallReport;

public record GetCallReportQuery(
    string[]? ExtensionNumbers,
    DateTime StartDate,
    DateTime EndDate,
    string? Direction,
    bool? IsAnswered,
    string? Caller,
    string? Callee,
    int? PageNumber = 1,
    int? PageSize = 10
) : IRequest<Result<CallReportResultResponse>>;

public class CallReportResultResponse
{
    public int TotalCount { get; set; }
    public List<CallReportResponse>? CallReportDtoList { get; set; }
}

public class CallReportResponse
{
    public string? HistoryOfTheCall { get; set; }
    public string? Caller { get; set; }
    public string? CallerName { get; set; }
    public string? Callee { get; set; }
    public string? CalleeName { get; set; }
    public string? Direction { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? AnsweredTime { get; set; }
    public DateTime? EndTime { get; set; }
    public int? TalkDurationInSeconds { get; set; }
    public int? TotalDurationInSeconds { get; set; }
    public bool? IsAnswered { get; set; }
    public string? Extension { get; set; }
    public string? RecordingUrl { get; set; }
    public string? Transcription { get; set; }
}
