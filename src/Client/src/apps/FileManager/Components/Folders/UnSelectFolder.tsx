import { Col, Image, Typography } from "antd";
import unSelectFolder from "@/assets/Images/unselect_folder.gif";
import { useTranslation } from "react-i18next";

const UnSelectFolder = () => {
  const { Text } = Typography;
  const { t } = useTranslation();
  return (
    <>
      <Col
        xs={24}
        className="!h-screen !flex items-center justify-center !bg-gray-50"
      >
        <div className="!flex flex-col  justify-center items-center !w-[700px] !h-[700px] !rounded-sm !p-4">
          <Image
            preview={false}
            src={unSelectFolder}
            width={"200px"}
            height={"200px"}
            className="!object-contain"
          />
          <Text className="!text-lg !font-bold">
            {t("fileManager.unSelectFileTitle")}
          </Text>

          <Text className="!text-xs !text-gray-500 ">
            {t("fileManager.unSelectFileDesc")}
          </Text>
        </div>
      </Col>
    </>
  );
};

export default UnSelectFolder;
