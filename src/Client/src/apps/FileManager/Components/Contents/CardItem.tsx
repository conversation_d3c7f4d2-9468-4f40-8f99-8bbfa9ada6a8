import { FC } from "react";
import { Card, Typography, Checkbox, Image, Tooltip, Col, Row } from "antd";
import dayjs from "dayjs";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import { useTranslation } from "react-i18next";
import videoIcon from "@/assets/Images/video_icon.png";
import fileIcon from "@/assets/Images/file_icon.png";
import { EditOutlined, FormOutlined } from "@ant-design/icons";
import { useDispatch } from "react-redux";
import { handleSetIsShowRenameFileModal, handleSetIsShowVideoPlayer, handleSetSelectedFile } from "../../ClientSideStates";

const { Text } = Typography;

interface Props {
  item: any;
  isSelected: boolean;
  onSelect: (checked: boolean) => void;
}

const CardItem: FC<Props> = ({ item, isSelected, onSelect }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch()
  const formatSize = (value: number) => {
    if (value >= 1024 * 1024) return `${(value / (1024 * 1024)).toFixed(2)} MB`;
    if (value >= 1024) return `${(value / 1024).toFixed(2)} KB`;
    return `${value} Byte`;
  };
  let src = "";
  let type = "";
  let videoFormats = ["mp4", "webm", "ogg", "mov", "avi", "mkv", "flv", "wmv"];
  if (item?.MimeType?.includes("image")) {
    src = `${setBackEndUrl()}/Uploads/${item?.StoragePath}`;
    type = "image";
  } else if (
    videoFormats.some((ext) => item.MimeType.toLowerCase().includes(ext))
  ) {
    src = videoIcon;
    type = "video";
  } else {
    src = fileIcon;
    type = "file";
  }

  return (
    <Card
      cover={
        <Image
          src={src}
          className={`!object-contain ${type==="video"?"cursor-pointer":""}`}
          width={"100%"}
          height={"100px"}
          preview={type === "image"}
          onClick={async()=>{
            if(type==="video")
            {
              await dispatch(handleSetSelectedFile({data:item}))
              dispatch(handleSetIsShowVideoPlayer({status:true}))
            }
          }}
        />
      }
      size="small"
    
      className="mb-2 !h-[150px]"
      title={
        <Row >
          <Col xs={24} xl={20} className="!h-[50px] !overflow-auto ">

          <Text className="!text-xs !text-wrap"
        
          >{item.FileName+ "omid ebadi ghareh omid ebadi ghareh ba"}</Text>
          </Col>
          <Col xs={24} xl={4} className="!flex items-center justify-end gap-1">
            <Checkbox
              checked={isSelected}
              onChange={(e) => onSelect(e.target.checked)}
            />
            <Tooltip title={t("fileManager.editName")}>
              <EditOutlined className="!text-blue-500 !text-xs cursor-pointer" onClick={async()=>{
               await  dispatch(handleSetSelectedFile({data:item}))
               dispatch(handleSetIsShowRenameFileModal({status:true}))
              }} />
            </Tooltip>
          </Col>
        </Row>
      }
    >
     
      
     
     
    </Card>
  );
};

export default CardItem;
