import { List, Pagination, Spin } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetFolderContents } from "../../ServerSideStates";
import {
  handleSetFolderContentDetailsFilter,
  handleSetFolderContentListSelectedItems,
} from "../../ClientSideStates";
import CardItem from "./CardItem";

const CardItems = () => {
  const dispatch = useDispatch();
  const { contentsFilter: filter, folderContentsListSelectedIds, selectedFolder, } = useSelector(
    (state: RootState) => state.folder
  );

  const folderDetails = useGetFolderContents({
    ...filter,
    folderId: selectedFolder?.Id,
  });

  const handleSelectItem = (itemId: string, checked: boolean) => {
    const currentIds = new Set(folderContentsListSelectedIds);
    checked ? currentIds.add(itemId) : currentIds.delete(itemId);
    dispatch(
      handleSetFolderContentListSelectedItems({
        selectedIds: Array.from(currentIds),
        selectedItems: folderDetails?.data?.Value?.filter((x) =>
          currentIds.has(x.Id)
        ),
      })
    );
  };

  const handleChangePagination = (page: number, pageSize: number) => {
    dispatch(
      handleSetFolderContentDetailsFilter({
        filter: { ...filter, PageNumber: page, PageSize: pageSize },
      })
    );
  };

  const data = folderDetails?.data?.Value || [];
  const loading = folderDetails.isLoading || folderDetails.isFetching;


 
  return (
    <div className="p-2">
      <Spin spinning={loading}>
        <List
          grid={{ gutter: 16, column: 5 }}
          dataSource={data}
          renderItem={(item:any) => (
            <List.Item key={item.Id}>
              <CardItem
                item={item}
                isSelected={folderContentsListSelectedIds.includes(item.Id)}
                onSelect={(checked) => handleSelectItem(item.Id, checked)}
              />
            </List.Item>
          )}
        />
        <div className="flex justify-end mt-4">
          <Pagination
            size="small"
            current={folderDetails.data?.PageNumber}
            total={folderDetails.data?.FilteredCount || 0}
            pageSize={30}
            showSizeChanger
            onChange={handleChangePagination}
            showTotal={(total) => `${total}`}
          />
        </div>
      </Spin>

      
    </div>
  );
};

export default CardItems;
