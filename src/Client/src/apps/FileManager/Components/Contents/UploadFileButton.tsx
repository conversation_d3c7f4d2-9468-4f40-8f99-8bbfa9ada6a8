import { useState } from "react";
import endPoints from "../../EndPoints";
import { useQueryClient } from "react-query";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { UploadOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { Drawer } from "antd";
import ImportFileIndex from "@/apps/Import/ImportIndex";
import { bulkUploadFile } from "../../Services";

const UploadFileButton = () => {
  const { t } = useTranslation();
  const [isShowUploadFileDrawer, setIsShowUploadFileDrawer] = useState(false);
  const queryClient = useQueryClient();

  return (
    <>
      <MazakaButton
        icon={<UploadOutlined />}
        onClick={() => {
          setIsShowUploadFileDrawer(true);
        }}
      >
        {t("fileManager.uploadFile")}
      </MazakaButton>

      <Drawer
        title={t("fileManager.uploadFile")}
        open={isShowUploadFileDrawer}
        onClose={() => {
          setIsShowUploadFileDrawer(false);
        }}
        width={"50%"}
      >
        <ImportFileIndex
          uploaderTitle={t("users.import.uploaderFileTitle")}
          uploaderDesc={t("users.import.uploaderFileDesc")}
          type="fileManager"
          multiple={true}
          accept="*"
          maxSize={20}
          startImportOperationService={bulkUploadFile}
          saveImportedFileService={undefined}
          onFinish={() => {
            queryClient.resetQueries({
              queryKey: endPoints.getContentDetailsFilter,
              exact: false,
            });
            setIsShowUploadFileDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default UploadFileButton;
