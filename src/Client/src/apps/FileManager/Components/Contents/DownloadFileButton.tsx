import { MazakaButton } from "@/apps/Common/MazakaButton";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { RootState } from "@/store/Reducers";
import { DownloadOutlined, LoadingOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { bulkDownloadFile } from "../../Services";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { handleSetFolderContentListSelectedItems } from "../../ClientSideStates";

const DownloadFileButton = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { folderContentsListSelectedIds } = useSelector(
    (state: RootState) => state.folder
  );
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const handleDownloadFiles = async () => {
    setIsLoading(true);
    try {
      const response = await bulkDownloadFile(folderContentsListSelectedIds);
  
      const blob = new Blob([response.data], {
        type: response.headers["content-type"],
      });

      
  
      const downloadUrl = window.URL.createObjectURL(blob);
      const anchor = document.createElement("a");
      anchor.href = downloadUrl;
      anchor.download = "files.zip"; // İstersen dynamic dosya adı
      document.body.appendChild(anchor);
      anchor.click();
      anchor.remove();
      window.URL.revokeObjectURL(downloadUrl);
  
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
  
      dispatch(
        handleSetFolderContentListSelectedItems({
          selectedIds: [],
          selectedItems: [],
        })
      );
    } catch (error) {
      showErrorCatching(error, null, false, t);
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <>
      <MazakaButton
        className="!text-xs"
        icon={isLoading ? <LoadingOutlined /> : <DownloadOutlined />}
        status={`${
          folderContentsListSelectedIds?.length > 0 ? "save" : "error"
        }`}
        disabled={
          folderContentsListSelectedIds?.length > 0 ? false : true || isLoading
        }
        loading={isLoading}
        onClick={handleDownloadFiles}
      >
        <span className=" !flex items-center gap-1 ">
          <span className="!text-xs ">{t("fileManager.downloadFile")}</span>
          {folderContentsListSelectedIds?.length > 0 && (
            <span className="!text-[10px] ">
              ({folderContentsListSelectedIds?.length})
            </span>
          )}
        </span>
      </MazakaButton>
    </>
  );
};

export default DownloadFileButton;
