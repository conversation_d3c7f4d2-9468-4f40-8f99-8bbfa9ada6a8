import { RootState } from "@/store/Reducers";
import { useDispatch, useSelector } from "react-redux";
import ListItems from "./ListItems";
import { Col, Modal, Row } from "antd";
import ViewingModes from "./ViewingModes";
import CardItems from "./CardItems";
import VideoPlayer from "./VideoPlayer";
import { handleSetIsShowRenameFileModal, handleSetIsShowVideoPlayer, handleSetSelectedFile } from "../../ClientSideStates";
import { useTranslation } from "react-i18next";
import AddOrUpdateFileName from "./AddOrUpdateFileName";

const ListIndex = () => {
  const dispatch = useDispatch()
  const {t} = useTranslation()
  const { selectedFolder, activeViewingMode,selectedFile,isShowVideoPlayer,isShowRenameModal } = useSelector(
    (state: RootState) => state.folder
  );
  return (
    <Row gutter={[0, 0]} className={`!mt-2 ${selectedFolder?.Id?"":"!bg-gray-50"}`}>
      {
        selectedFolder?.Id&&
      <Col xs={24} className="!flex justify-end">
        <ViewingModes />
      </Col>
      }
      <Col xs={24}>
       
          <>
            {activeViewingMode === "table" ? (
              <>
                <ListItems />
              </>
            ) : (
              <>
                <CardItems />
              </>
            )}
          </>
       
      </Col>

      <Modal
      title={selectedFile?.FileName}
      footer={false}
      open={isShowVideoPlayer}
      onCancel={async()=>{
        await dispatch(handleSetIsShowVideoPlayer({status:false}))
        dispatch(handleSetSelectedFile({data:null}))
      }}
      width={"60%"}
      >
        <VideoPlayer/>
      </Modal>
      <Modal
      title={t("fileManager.editName")}
      open={isShowRenameModal}
      onCancel={async()=>{
        await dispatch(handleSetIsShowRenameFileModal({status:false}))
        dispatch(handleSetSelectedFile({data:null}))
      }}
      footer={false}
      >
        <AddOrUpdateFileName
        onFinish={async()=>{
          await dispatch(handleSetIsShowRenameFileModal({status:false}))
          dispatch(handleSetSelectedFile({data:null}))
        }}
        />

      </Modal>
    </Row>
  );
};

export default ListIndex;
