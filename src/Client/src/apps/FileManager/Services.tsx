import { DataResponse } from "@/services/BaseResponseModel";
import endpoints from "./EndPoints";
import headers from "@/services/Headers.json";
import { deleteRequest, get, post, postFile, put } from "@/services/Client";
import { CreateUrlFilter } from "@/helpers/CreateURLFilter";

export const getFolderListFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getFolderListFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getContentDetailsFilter = async (
  inputFilter: any
): Promise<DataResponse<any>> => {
  const query = inputFilter ? CreateUrlFilter(inputFilter) : null;

  const url = `${endpoints.getContentDetailsFilter}?${query || ""}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const bulkUploadFile = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.bulkUploadFile}`;
  const config = headers.content_type.multipart_form_data;
  return post<DataResponse<any>>(url, data, config);
};

export const bulkDeleteFile = async (data: any): Promise<DataResponse<any>> => {
  let ids  = data?.ids||[]
  const query = ids.map((id:string) => `fileIds=${encodeURIComponent(id)}`).join("&");
  const url = `${endpoints.bulkDeleteFile}?${query}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};

export const bulkDownloadFile = async (data: any): Promise<any> => {
  const url = `${endpoints.BulkDownloadFile}`;
  const config = {
    accept:"application/zip",
    headers: {
      "Content-Type": "application/json", // veya backend ne istiyorsa
    },
    responseType: "blob" as const, // önemli
  };

  return postFile<any>(url, data, config); // burada DataResponse değil, binary döndüğü için `any` kullanılıyor
};



export const renameFileName = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.renameFileName}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};



export const createFolder = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.createFolder}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};

export const renameFolderName = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.renameFolderName}/${data.Id}/rename`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const deleteFolder = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteFolder}/${data.Id}?recursive=true`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
