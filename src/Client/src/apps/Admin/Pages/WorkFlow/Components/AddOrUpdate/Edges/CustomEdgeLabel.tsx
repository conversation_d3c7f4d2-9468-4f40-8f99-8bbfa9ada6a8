import { useEffect, useRef, useState } from "react";
import {
  BaseEdge,
  EdgeLabel<PERSON>enderer,
  EdgeProps,
} from "@xyflow/react";
import { BulbFilled } from "@ant-design/icons";
import { drag } from "d3-drag";
import { select } from "d3-selection";

type ControlPoint = { x: number; y: number };

export default function CustomEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  markerEnd,
  sourcePosition,
  targetPosition,
  data,
}: EdgeProps) {
  const initialPoints: ControlPoint[] = [
    { x: sourceX + (targetX - sourceX) / 3, y: sourceY + (targetY - sourceY) / 3 },
    { x: sourceX + (targetX - sourceX) * 2 / 3, y: sourceY + (targetY - sourceY) * 2 / 3 },
  ];

  const [controlPoints, setControlPoints] = useState<ControlPoint[]>(
    () => JSON.parse(localStorage.getItem(`edge-${id}`) || "null") || initialPoints
  );

  const pointRefs = useRef<(HTMLDivElement | null)[]>([]);
  const zoom = 1; // İsteğe göre ReactFlow'dan alınabilir

  useEffect(() => {
    pointRefs.current.forEach((ref, index) => {
      if (ref) {
        const d3Selection = select(ref);
        d3Selection.call(
          drag().on("drag", (e) => {
            setControlPoints((prev) => {
              const updated = [...prev];
              updated[index] = {
                x: updated[index].x + e.dx / zoom,
                y: updated[index].y + e.dy / zoom,
              };
              localStorage.setItem(`edge-${id}`, JSON.stringify(updated));
              return updated;
            });
          })
        );
      }
    });
  }, [pointRefs.current.length]);

  // Edge path oluştur: source → controlPoints → target
  const path = `M ${sourceX},${sourceY} ${controlPoints
    .map((p) => `L ${p.x},${p.y}`)
    .join(" ")} L ${targetX},${targetY}`;

  // Label konumunu çizgi ortasına göre hesapla
  const labelPosition = (() => {
    const tempPath = document.createElementNS("http://www.w3.org/2000/svg", "path");
    tempPath.setAttribute("d", path);
    const length = tempPath.getTotalLength();
    const midpoint = tempPath.getPointAtLength(length / 2);
    return { x: midpoint.x, y: midpoint.y };
  })();

  return (
    <>
      {/* Marker tanımı */}
      <svg style={{ height: 0 }}>
        <defs>
          <marker
            id="custom-arrow"
            viewBox="0 0 10 10"
            refX="5"
            refY="5"
            markerWidth="10"
            markerHeight="10"
            orient="auto-start-reverse"
          >
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#0096d1" />
          </marker>
        </defs>
      </svg>

      {/* Çizgi */}
      <BaseEdge id={id} path={path} markerEnd="url(#custom-arrow)" />

      <EdgeLabelRenderer>
        {/* Etiket */}
        <div
          id="label-wrapper"
          style={{
            position: "absolute",
            left: 0,
            transform: `translate(-50%, -50%) translate(${labelPosition.x}px, ${labelPosition.y}px)`,
            pointerEvents: "all",
            background: "#fff",
            border: "1px solid #ccc",
            borderRadius: "4px",
            zIndex: 1000,
          }}
          className="nodrag nopan !flex gap-1 items-center !p-1 cursor-pointer !text-xs !text-[#35b214]"
        >
          {data?.ruleCount > 0 && <BulbFilled className="!text-yellow-400 !text-base" />}
          {data?.name || ""}
        </div>

        {/* Kontrol Noktaları */}
        {controlPoints.map((point, index) => (
          <div
            key={index}
            ref={(el) => (pointRefs.current[index] = el)}
            style={{
              position: "absolute",
              left: point.x - 4,
              top: point.y - 4,
              width: 8,
              height: 8,
              background: "#0096d1",
              borderRadius: "50%",
              cursor: "grab",
              zIndex: 1000,
              pointerEvents: "all",
            }}
          />
        ))}
      </EdgeLabelRenderer>
    </>
  );
}
