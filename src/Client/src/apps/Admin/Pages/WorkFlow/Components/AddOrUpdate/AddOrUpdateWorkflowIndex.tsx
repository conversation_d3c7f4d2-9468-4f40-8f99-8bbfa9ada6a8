import PageTitle from "@/apps/Common/PageTitle";
import { <PERSON><PERSON>, Col, Divider,  <PERSON> } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";

import { useNavigate, useSearchParams } from "react-router-dom";
import EdgeTypesFlow from "./Flow";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { useTranslation } from "react-i18next";
import { commonRoutePrefix } from "@/routes/Prefix";
import { ReactFlowProvider } from '@xyflow/react';

const AddOrUpdateWorkflowIndex = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams()


  return (
    <>
      <Col xs={24}>
        <Row className="!px-2">
          <Col xs={24} className="">
            <div className="!flex gap-1 items-center !p-2">
              <ArrowLeftOutlined
                onClick={() => {
                  queryClient.resetQueries({
                    queryKey: endPoints.getWorkFlowListFilter,
                    exact: false,
                  });
                  navigate(`${commonRoutePrefix}/workflow`);
                
                }}
                className="!text-xl !text-gray-400"
              />

              <PageTitle title={t("workFlow.editWorkFlow")} isSubTitle />
              {
                searchParams.get("flowName")&&
              <span>({searchParams.get("flowName")||""})</span>
              }

              
            </div>
          </Col>
          <Col xs={24}>
            <Divider className="!m-0" />
          </Col>
        
          <Col xs={24}>
          <ReactFlowProvider>

              <EdgeTypesFlow />
          </ReactFlowProvider>
          
          </Col>
        </Row>
      </Col>
      
    </>
  );
};

export default AddOrUpdateWorkflowIndex;
