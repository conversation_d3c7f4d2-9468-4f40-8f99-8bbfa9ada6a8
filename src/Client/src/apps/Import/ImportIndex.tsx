import { Col,  Row } from "antd";
import { FC, useState } from "react";
import UploadFile from "./Components/UploadFile";
import FormContent from "./Components/FormContent";

interface ImportFileIndexProps {
  type: "userList" | "customerList" | "tempCustomerList" | "autoDialer" |"fileManager";
  startImportOperationService: any;
  saveImportedFileService: any;
  onFinish?: any;
  uploaderTitle?: string;
  uploaderDesc?: string;
  multiple?:boolean
  accept?:string,
  maxSize?:number
}

const ImportFileIndex: FC<ImportFileIndexProps> = (props) => {
  const [fileList, setFileList] = useState<any[]>([]);
  const [fileData, setFileData] = useState<null | any>(null);
 

  return (
    <>
      <Col xs={24}>
        <Row gutter={[20, 20]}>
          <Col xs={24}>
            <UploadFile
              title={props.uploaderTitle}
            
              inputMaxSize={props.maxSize}
              multiple={props.multiple}
              accept={props.accept}
              uploaderDesc={props.uploaderDesc}
              fileList={fileList}
              setFileList={setFileList}
              setFileData={setFileData}
              fileData={fileData}
              {...props}
              onFinish={() => {
              
                setFileData(null);
                setFileList([]);
                props.onFinish();
              }}
            />
          </Col>

          {props.type !=="fileManager"&&fileData && (
            <>
              <Col xs={24}>
                <FormContent
                  type={props.type}
                  fileData={fileData}
                  saveImportedFileService={props.saveImportedFileService}
                  onFinish={() => {
                    setFileData(null);
                    setFileList([]);
                    props.onFinish();
                  }}
                />
              </Col>
            </>
          )}
        </Row>
      </Col>
    </>
  );
};

export default ImportFileIndex;
