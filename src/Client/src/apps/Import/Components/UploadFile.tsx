import React, { useState } from "react";
import type { UploadProps } from "antd";
import { Col, Row, Upload } from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const { Dragger } = Upload;

interface UploaderFileProps {
  type: "userList" | "customerList" | "tempCustomerList" | "autoDialer"|"fileManager";
  startImportOperationService: any;
  saveImportedFileService: any;
  setFileData: any;
  fileData: any;
  fileList: any[];
  setFileList: any;
  title?: string;
  desc?: string;
  multiple?:boolean
  accept?:string
  inputMaxSize?:number
  onFinish?:any
}

const UploaderFile: React.FC<UploaderFileProps> = (props) => {
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();
  const {selectedFolder} = useSelector((state:RootState)=>state.folder)
 

  const handleBeforeUpload = (file: any, fileList: any[]) => {
    const maxSize = props.inputMaxSize || 5;
   
    const isSizeValid = file.size / 1024 / 1024 < maxSize;
    const isAlreadyExist = props.fileList.some((f) => f.name === file.name);
  
    if (!isSizeValid) {
      // Aynı dosya ismiyle birden fazla hata notification'u engelle
      if (!props.fileList.some((f) => f.name === file.name && f.error)) {
        openNotificationWithIcon("error", `${file.name} boyutu ${maxSize}MB'yi geçemez`);
      }
      return Upload.LIST_IGNORE;
    }
  
    if (isAlreadyExist) {
      return Upload.LIST_IGNORE;
    }
  
    // dosya eklenebilir
    props.setFileList((prev: any) => [...prev, file]);
    return false;
  };
  

  

  const handleRemove = (fileName: string) => {

    props.setFileList((prevList: any) =>
      prevList.filter((file: any) => file.name !== fileName)
    );
  };

  const uploaderAttrs: UploadProps = {
    name: "file",
    multiple: props?.multiple||false,
    accept: props.accept||".xlsx",
    showUploadList: false,
    beforeUpload:handleBeforeUpload,
    
    disabled: isLoading,
    customRequest:()=>{
      
    }
  };

 

 const handleStartImportFile = async () => {
  if (!props.fileList || props.fileList.length === 0) {
    openNotificationWithIcon("error", "Dosya Seçimi Zorunlu");
    return false;
  }

  setIsLoading(true);
  try {
   
  
    const formData = new FormData();

    if (props.multiple) {
      if(props.type==="fileManager")
      {
        formData.append("FolderId",selectedFolder?.Id)
      }

    

      props.fileList.forEach((file: any, index: number) => {
        formData.append("Files", file); 
      });
    } else {
      formData.append("file", props.fileList[0]); 
    }

    const response = await props.startImportOperationService(formData);
    if(props.type ==="fileManager")
    {
     
      props.onFinish()
    }
    else{

      if (response?.Value) {
        props.setFileData(response?.Value);
      } else {
        props.setFileData(null);
      }
    }
  } catch (error) {
    showErrorCatching(error, null, false, t);
  } finally {
    setIsLoading(false);
  }
};




  return (
    <div className="!flex !flex-col gap-3">
      <div>
        <Dragger {...uploaderAttrs}>
          <p className="ant-upload-drag-icon !flex justify-center">
            <svg
              fill="#808080"
              width={40}
              height={40}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 512 512"
            >
              <path d="M8 256C8 119 119 8 256 8s248 111 248 248-111 248-248 248S8 393 8 256zm292 116V256h70.9c10.7 0 16.1-13 8.5-20.5L264.5 121.2c-4.7-4.7-12.2-4.7-16.9 0l-115 114.3c-7.6 7.6-2.2 20.5 8.5 20.5H212v116c0 6.6 5.4 12 12 12h64c6.6 0 12-5.4 12-12z"></path>
            </svg>
          </p>
          <p className="ant-upload-text">{props.title}</p>
          <p className="ant-upload-hint">{props.desc}</p>
        </Dragger>
        <Col xs={24} className="!mt-4">
          <Row className="!h-full">
            <Col xs={21}>
              {props.fileList.length > 0 && (
                <div className="mt-4 space-y-2">
                  {props.fileList.map((file) => (
                    <div
                      key={file.name}
                      className="flex justify-between items-center p-2 border rounded-md"
                    >
                      <span className="truncate">{file.name}</span>
                      <DeleteOutlined
                        className="text-red-500 cursor-pointer"
                        onClick={() => handleRemove(file.name)}
                      />
                    </div>
                  ))}
                </div>
              )}
            </Col>
            <Col xs={3} className="!flex justify-end !items-center !h-full ">
              <MazakaButton
              className="!mt-6"
                disabled={isLoading}
                loading={isLoading}
                htmlType="button"
                onClick={handleStartImportFile}
                status="save"
              >
                {t("users.import.upload")}
              </MazakaButton>

            </Col>
           
          </Row>
        </Col>
       
      </div>
    </div>
  );
};

export default UploaderFile;
