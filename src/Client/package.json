{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.3.1", "@ant-design/pro-flow": "^1.3.12", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@microsoft/signalr": "^8.0.7", "@react-google-maps/api": "^2.20.7", "@reduxjs/toolkit": "^2.8.2", "@xyflow/react": "^12.8.1", "antd": "^5.26.1", "axios": "^1.10.0", "d3": "^7.9.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "libphonenumber-js": "^1.12.9", "postcss": "^8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.3", "react-number-format": "^5.4.4", "react-phone-input-2": "^2.15.1", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-redux": "^9.1.0", "react-router-dom": "^6.22.3", "reactflow": "^11.11.4", "recharts": "^2.15.4", "tailwindcss": "^3.4.1", "use-debounce": "^10.0.5"}, "devDependencies": {"@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.20", "typescript": "^5.8.3", "vite": "^6.3.5"}}